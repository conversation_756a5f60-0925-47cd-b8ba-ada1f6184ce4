import { ref, onUnmounted, Ref } from 'vue';
import { Graph, Shape } from '@antv/x6';
import { Transform } from '@antv/x6-plugin-transform';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import type {
  WorkflowGraphInstance,
  SwimLaneData,
  BackendWorkflowFormat,
  BackendWorkflowNode,
  BackendWorkflowEdge,
  CompleteWorkflowData,
} from '../types/workflow';

/**
 * 工作流图形实例管理 Hook
 * 1、读取data渲染流程图
 * 2、流程图转JSON
 */
export function useWorkflowGraph(): WorkflowGraphInstance {
  const graph = ref<Graph | null>(null) as Ref<Graph | null>;

  /**
   * 获取容器的实际尺寸
   */
  const getContainerSize = (container: HTMLElement) => {
    // 确保容器有尺寸，如果没有则使用默认值
    const rect = container.getBoundingClientRect();
    const width = rect.width > 0 ? rect.width : container.offsetWidth || 800;
    const height = rect.height > 0 ? rect.height : container.offsetHeight || 600;
    return { width, height };
  };

  /**
   * 初始化图形实例
   */
  const initGraph = (container: HTMLElement) => {
    if (!container) {
      return;
    }

    const { width, height } = getContainerSize(container);
    // 创建图表实例
    graph.value = new Graph({
      container,
      width,
      height,
      grid: {
        size: 10,
        visible: true,
        type: 'dot',
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      // 设置事件监听器为passive模式以提高性能
      preventDefaultBlankAction: false,
      // 启用交互功能
      interacting: {
        nodeMovable: true,
        edgeMovable: true,
        edgeLabelMovable: true,
        arrowheadMovable: true,
        vertexMovable: true,
        vertexAddable: true,
        vertexDeletable: true,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: { radius: 8 },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: { radius: 20 },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: { name: 'block', width: 12, height: 8 },
              },
            },
            data: {
              label: '',
              condition: '',
              description: '',
            },
            zIndex: 0,
          });
        },
        validateConnection({ sourceView, targetView, targetMagnet }) {
          // 必须有目标连接桩
          if (!targetMagnet) {
            return false;
          }

          // 获取源节点和目标节点
          const sourceNode = sourceView?.cell;
          const targetNode = targetView?.cell;

          // 检查源节点和目标节点是否存在
          if (!sourceNode || !targetNode) {
            return false;
          }

          // 不允许连接到自己
          if (sourceNode === targetNode) {
            return false;
          }

          // 不允许泳道节点参与连线
          if (sourceNode.shape === 'lane' || targetNode.shape === 'lane') {
            return false;
          }

          // 只允许普通节点之间连线
          const allowedShapes = ['lane-rect', 'lane-polygon'];
          if (!allowedShapes.includes(sourceNode.shape) || !allowedShapes.includes(targetNode.shape)) {
            return false;
          }

          return true;
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: { attrs: { fill: '#5F95FF', stroke: '#5F95FF' } },
        },
      },
      translating: {
        restrict(cellView: any) {
          // 检查 cellView 是否存在
          if (!cellView || !cellView.cell) {
            return null;
          }
          const cell = cellView.cell;

          // 检查 cell 是否有必要的方法
          if (!cell || typeof cell.prop !== 'function' || typeof cell.getBBox !== 'function') {
            return null;
          }

          try {
            const parentId = cell.prop('parent');
            if (parentId) {
              const parentNode = graph.value?.getCellById(parentId);
              if (parentNode && typeof parentNode.getBBox === 'function') {
                return parentNode.getBBox().moveAndExpand({
                  x: 0,
                  y: 30,
                  width: 0,
                  height: -30,
                });
              }
            }
            return cell.getBBox();
          } catch (error) {
            // 如果出现任何错误，返回 null 表示不限制移动
            return null;
          }
        },
      },
    });

    // 初始化插件
    if (graph.value) {
      graph.value
        .use(new Transform({ resizing: true, rotating: true }))
        .use(new Selection({ rubberband: true, showNodeSelectionBox: true }))
        .use(new Snapline());

      // 监听节点添加事件，确保新添加的节点具有正确的交互属性
      graph.value.on('node:added', ({ node }) => {
        // 检查节点是否存在且有必要的方法
        if (!node || typeof node.prop !== 'function' || typeof node.getAttrs !== 'function') {
          return;
        }

        try {
          // 确保节点可以移动
          if (node.prop('movable') !== false) {
            node.prop('movable', true);
          }

          // 确保节点具有正确的样式
          const currentAttrs = node.getAttrs();
          if (currentAttrs && currentAttrs.body && !currentAttrs.body.cursor) {
            node.setAttrs({
              body: {
                ...currentAttrs.body,
                cursor: 'move',
              },
            });
          }
          if (currentAttrs && currentAttrs.text && !currentAttrs.text.cursor) {
            node.setAttrs({
              text: {
                ...currentAttrs.text,
                cursor: 'move',
              },
            });
          }
        } catch (error) {
          // 如果设置属性时出错，静默处理
        }
      });

      // 添加鼠标事件监听器来显示/隐藏连接桩
      graph.value.on('node:mouseenter', ({ node }) => {
        // 只对可连接的节点显示连接桩
        if (node.shape === 'lane-rect' || node.shape === 'lane-polygon') {
          const ports = node.getPorts();
          ports.forEach((port) => {
            node.setPortProp(port.id!, 'attrs/circle/style/visibility', 'visible');
          });
        }
      });

      graph.value.on('node:mouseleave', ({ node }) => {
        // 隐藏连接桩
        if (node.shape === 'lane-rect' || node.shape === 'lane-polygon') {
          const ports = node.getPorts();
          ports.forEach((port) => {
            node.setPortProp(port.id!, 'attrs/circle/style/visibility', 'hidden');
          });
        }
      });
    }
  };

  /**
   * 调整图形尺寸
   */
  const resizeGraph = (container?: HTMLElement) => {
    if (!graph.value) return;

    const targetContainer = container || graph.value.container;
    if (!targetContainer) return;

    const { width, height } = getContainerSize(targetContainer);

    // 只有当尺寸真正发生变化时才调整
    const currentSize = graph.value.getGraphArea();
    if (currentSize.width !== width || currentSize.height !== height) {
      console.log('Resizing graph:', { from: currentSize, to: { width, height } });
      graph.value.resize(width, height);

      // 调整后重新适配内容
      setTimeout(() => {
        if (graph.value && graph.value.getCells().length > 0) {
          graph.value.zoomToFit({ padding: 10, maxScale: 1 });
        }
      }, 100);
    }
  };

  /**
   * 销毁图形实例
   */
  const destroyGraph = () => {
    if (graph.value) {
      graph.value.dispose();
      graph.value = null;
    }
  };

  /**
   * 将X6格式转换为后端工作流引擎格式
   */
  const convertToBackendFormat = (x6Data: any): BackendWorkflowFormat => {
    const nodeList: BackendWorkflowNode[] = [];
    const edgeList: BackendWorkflowEdge[] = [];

    if (!x6Data || !x6Data.cells) {
      return { nodeList, edgeList };
    }

    // 先获取所有泳道节点，建立泳道映射
    const lanes = x6Data.cells.filter((cell: any) => cell.shape === 'lane');
    const laneMap = new Map();
    lanes.forEach((lane: any) => {
      laneMap.set(lane.id, lane.data || {});
    });

    // 处理节点
    const nodes = x6Data.cells.filter((cell: any) => cell.shape && !cell.source && !cell.target && cell.shape !== 'lane');

    nodes.forEach((node: any, index: number) => {
      const data = node.data || {};
      const position = node.position || { x: 0, y: 0 };
      const size = node.size || { width: 100, height: 60 };

      // 获取节点所属泳道的委托人/委托机构信息
      let assignee = '';
      let assigneeOrgCode = '';

      // 查找节点的父节点（泳道）
      const parentId = node.parent;
      if (parentId && laneMap.has(parentId)) {
        const laneData = laneMap.get(parentId);
        assignee = laneData.assignee || '';
        assigneeOrgCode = laneData.assigneeOrgCode || '';
      }

      nodeList.push({
        id: data.id || node.id || index + 1,
        name: data.name || node.label || `节点${index + 1}`,
        type: data.type || 'process',
        assignee: assignee,
        assigneeOrgCode: assigneeOrgCode,
        handler: data.handler || '',
        description: data.description || '',
        x: position.x,
        y: position.y,
        width: size.width,
        height: size.height,
      });
    });

    // 处理边
    const edges = x6Data.cells.filter((cell: any) => cell.source && cell.target);

    edges.forEach((edge: any, index: number) => {
      const data = edge.data || {};
      const labels = edge.labels || [];
      const label = labels.length > 0 ? labels[0].attrs?.text?.text || '' : '';

      edgeList.push({
        id: data.id || edge.id || index + 1,
        sourceId: edge.source.cell || edge.source,
        targetId: edge.target.cell || edge.target,
        condition: data.condition || '',
        label: data.label || label,
        description: data.description || '',
      });
    });

    return { nodeList, edgeList };
  };

  /**
   * 将后端工作流引擎格式转换为X6格式
   */
  const convertFromBackendFormat = (backendData: BackendWorkflowFormat): any => {
    const cells: any[] = [];

    // 处理节点
    backendData.nodeList.forEach((node) => {
      cells.push({
        id: node.id.toString(),
        shape: 'custom-rect',
        position: { x: node.x || 100, y: node.y || 100 },
        size: { width: node.width || 100, height: node.height || 60 },
        label: node.name,
        data: {
          id: node.id,
          name: node.name,
          type: node.type,
          assignee: node.assignee || '',
          assigneeOrgCode: node.assigneeOrgCode || '',
          handler: node.handler || '',
          description: node.description || '',
        },
        attrs: {
          body: {
            stroke: '#5F95FF',
            fill: '#EFF4FF',
            strokeWidth: 1,
          },
          text: {
            fontSize: 12,
            fill: '#262626',
          },
        },
      });
    });

    // 处理边
    backendData.edgeList.forEach((edge) => {
      const edgeData: any = {
        id: edge.id.toString(),
        shape: 'edge',
        source: edge.sourceId.toString(),
        target: edge.targetId.toString(),
        data: {
          id: edge.id,
          condition: edge.condition || '',
          description: edge.description || '',
          label: edge.label || '',
        },
        attrs: {
          line: {
            stroke: '#A2B1C3',
            strokeWidth: 2,
            targetMarker: { name: 'block', width: 12, height: 8 },
          },
        },
      };

      // 如果有标签，添加标签
      if (edge.label) {
        edgeData.labels = [{ attrs: { text: { text: edge.label } } }];
      }

      cells.push(edgeData);
    });

    return { cells };
  };

  /**
   * 自动设置泳道内节点的父子关系
   */
  const setLaneParentRelations = (x6Data: any): any => {
    if (!x6Data || !x6Data.cells) {
      return x6Data;
    }

    // 获取所有泳道
    const lanes = x6Data.cells.filter((cell: any) => cell.shape === 'lane');
    // 获取所有普通节点（非泳道、非边）
    const nodes = x6Data.cells.filter((cell: any) =>
      cell.shape && cell.shape !== 'lane' && !cell.source && !cell.target
    );

    // 为每个节点检查是否在泳道内，如果在但没有设置父节点，则自动设置
    nodes.forEach((node: any) => {
      // 如果节点已经有父节点，跳过
      if (node.parent) {
        return;
      }

      const nodePosition = node.position || { x: 0, y: 0 };
      const nodeSize = node.size || { width: 100, height: 60 };

      // 节点的边界框
      const nodeBounds = {
        x: nodePosition.x,
        y: nodePosition.y,
        width: nodeSize.width,
        height: nodeSize.height,
        right: nodePosition.x + nodeSize.width,
        bottom: nodePosition.y + nodeSize.height
      };

      // 查找包含此节点的泳道
      for (const lane of lanes) {
        const lanePosition = lane.position || { x: 0, y: 0 };
        const laneSize = lane.size || { width: 200, height: 150 };

        // 泳道的边界框
        const laneBounds = {
          x: lanePosition.x,
          y: lanePosition.y,
          width: laneSize.width,
          height: laneSize.height,
          right: lanePosition.x + laneSize.width,
          bottom: lanePosition.y + laneSize.height
        };

        // 检查节点是否完全在泳道内
        if (nodeBounds.x >= laneBounds.x &&
            nodeBounds.y >= laneBounds.y &&
            nodeBounds.right <= laneBounds.right &&
            nodeBounds.bottom <= laneBounds.bottom) {
          // 设置父子关系
          node.parent = lane.id;
          console.log(`自动设置节点 ${node.id} 的父节点为泳道 ${lane.id}`);
          break; // 找到第一个包含的泳道就停止
        }
      }
    });

    return x6Data;
  };

  /**
   * 获取图形数据（包含两种格式）
   */
  const getGraphData = (): CompleteWorkflowData | null => {
    if (!graph.value) return null;

    let x6Format = graph.value.toJSON();

    // 在转换为后端格式之前，自动设置泳道内节点的父子关系
    x6Format = setLaneParentRelations(x6Format);

    const backendFormat = convertToBackendFormat(x6Format);

    return {
      x6Format,
      backendFormat,
      metadata: {
        name: '',
        businessKey: '',
        version: '1.0',
        description: '',
      },
    };
  };

  /**
   * 加载图形数据（支持多种格式）
   */
  const loadGraphData = (data: SwimLaneData | CompleteWorkflowData | BackendWorkflowFormat | any) => {
    if (!graph.value) {
      return;
    }
    // 清空现有内容
    graph.value.clearCells();

    let x6Data: any = null;

    // 判断数据格式并转换
    if (data.x6Format) {
      // CompleteWorkflowData格式
      x6Data = data.x6Format;
    } else if (data.nodeList && data.edgeList) {
      // BackendWorkflowFormat格式
      x6Data = convertFromBackendFormat(data as BackendWorkflowFormat);
    } else if (data.lanes || data.nodes || data.edges) {
      // SwimLaneData格式（原有格式）
      const swimLaneData = data as SwimLaneData;
      const cells: any[] = [];

      // 检查数据结构
      if (!swimLaneData.lanes) swimLaneData.lanes = [];
      if (!swimLaneData.nodes) swimLaneData.nodes = [];
      if (!swimLaneData.edges) swimLaneData.edges = [];

      // 1. 先创建泳道节点
      swimLaneData.lanes.forEach((lane) => {
        const laneNode = graph.value!.createNode(lane);
        cells.push(laneNode);
      });

      // 2. 处理节点
      swimLaneData.nodes.forEach((node) => {
        const nodeItem = graph.value!.createNode(node);
        cells.push(nodeItem);
      });

      // 3. 处理边
      swimLaneData.edges.forEach((edge) => {
        const edgeItem = graph.value!.createEdge(edge);
        cells.push(edgeItem);
      });

      graph.value.resetCells(cells);
      graph.value.zoomToFit({ padding: 10, maxScale: 1 });
      return;
    } else if (data.cells) {
      // 直接的X6格式
      x6Data = data;
    }

    // 加载X6格式数据
    if (x6Data && x6Data.cells) {
      graph.value.fromJSON(x6Data);
      graph.value.zoomToFit({ padding: 10, maxScale: 1 });
    }
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    destroyGraph();
  });

  return {
    graph,
    initGraph,
    destroyGraph,
    resizeGraph,
    getGraphData,
    loadGraphData,
    convertToBackendFormat,
    convertFromBackendFormat,
  };
}
