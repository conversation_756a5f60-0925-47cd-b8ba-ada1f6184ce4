<template>
  <div class="workflow-sidebar" :class="{ 'sidebar-collapsed': collapsed }" :style="{ width: collapsed ? '40px' : `${props.width}px` }">
    <!-- 折叠/展开按钮 -->
    <div class="sidebar-toggle" @click="toggleCollapse">
      <Icon :icon="collapsed ? 'ant-design:right-outlined' : 'ant-design:left-outlined'" />
    </div>

    <!-- 侧边栏内容 -->
    <div class="sidebar-content" v-show="!collapsed">
      <!-- 泳道属性面板 -->
      <div v-if="selectedNode && selectedNode.shape === 'lane'" class="property-panel">
        <div class="panel-header">
          <h3>泳道属性</h3>
          <div class="node-type">泳道</div>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="nodeProperties">
            <a-form-item label="泳道名称">
              <a-input
                v-model:value="nodeProperties.name"
                placeholder="请输入泳道名称"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>

            <a-form-item label="委托人">
              <a-input
                v-model:value="nodeProperties.assignee"
                placeholder="请输入委托人"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>

            <a-form-item label="委托机构">
              <a-input
                v-model:value="nodeProperties.assigneeOrgCode"
                placeholder="请输入委托机构代码"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>

            <a-form-item label="泳道描述">
              <a-textarea
                v-model:value="nodeProperties.description"
                placeholder="请输入泳道描述"
                :rows="3"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 普通节点属性面板 -->
      <div v-else-if="selectedNode" class="property-panel">
        <div class="panel-header">
          <h3>节点属性</h3>
          <div class="node-type">{{ getNodeTypeLabel(selectedNode.shape) }}</div>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="nodeProperties">
            <a-form-item label="节点名称">
              <a-input
                v-model:value="nodeProperties.name"
                placeholder="请输入节点名称"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>

            <a-form-item label="节点类型">
              <a-select v-model:value="nodeProperties.type" placeholder="请选择节点类型" @change="updateNodeProperties">
                <a-select-option value="start">开始节点</a-select-option>
                <a-select-option value="userTask">用户任务</a-select-option>
                <a-select-option value="end">结束节点</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 委托人字段：普通节点只读显示所属泳道的值 -->
            <a-form-item label="委托人">
              <a-input :value="getLaneAssigneeInfo().assignee" placeholder="由所属泳道设置" readonly disabled />
              <div class="field-hint">此字段由所属泳道设置，请在泳道属性中修改</div>
            </a-form-item>

            <!-- 委托机构字段：普通节点只读显示所属泳道的值 -->
            <a-form-item label="委托机构">
              <a-input :value="getLaneAssigneeInfo().assigneeOrgCode" placeholder="由所属泳道设置" readonly disabled />
              <div class="field-hint">此字段由所属泳道设置，请在泳道属性中修改</div>
            </a-form-item>

            <a-form-item label="回调接口">
              <a-input
                v-model:value="nodeProperties.handler"
                placeholder="请输入回调接口"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>

            <a-form-item label="节点描述">
              <a-textarea
                v-model:value="nodeProperties.description"
                placeholder="请输入节点描述"
                :rows="3"
                @change="updateNodeProperties"
                @blur="updateNodeProperties"
                @input="handleNodePropertyInput"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 边属性面板 -->
      <div v-else-if="selectedEdge" class="property-panel">
        <div class="panel-header">
          <h3>连线属性</h3>
          <div class="edge-type">连接线</div>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="edgeProperties">
            <a-form-item label="连线名称">
              <a-input
                v-model:value="edgeProperties.label"
                placeholder="请输入连线名称"
                @change="updateEdgeProperties"
                @blur="updateEdgeProperties"
                @input="handleEdgePropertyInput"
              />
            </a-form-item>

            <a-form-item label="条件表达式">
              <a-textarea
                v-model:value="edgeProperties.condition"
                placeholder="请输入条件表达式"
                :rows="3"
                @change="updateEdgeProperties"
                @blur="updateEdgeProperties"
                @input="handleEdgePropertyInput"
              />
            </a-form-item>

            <a-form-item label="连线描述">
              <a-textarea
                v-model:value="edgeProperties.description"
                placeholder="请输入连线描述"
                :rows="3"
                @change="updateEdgeProperties"
                @blur="updateEdgeProperties"
                @input="handleEdgePropertyInput"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 画布属性面板 -->
      <div v-else class="property-panel">
        <div class="panel-header">
          <h3>画布属性</h3>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="canvasProperties">
            <a-form-item label="工作流名称">
              <a-input v-model:value="canvasProperties.name" placeholder="请输入工作流名称" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="业务标识">
              <a-input v-model:value="canvasProperties.businessKey" placeholder="请输入业务标识" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="版本号">
              <a-input v-model:value="canvasProperties.version" placeholder="请输入版本号" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="工作流描述">
              <a-textarea v-model:value="canvasProperties.description" placeholder="请输入工作流描述" :rows="4" @change="updateCanvasProperties" />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-panel">
        <div class="panel-header">
          <h3>统计信息</h3>
        </div>

        <div class="panel-body">
          <div class="stats-item">
            <span class="stats-label">节点数量:</span>
            <span class="stats-value">{{ stats.nodeCount }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">连线数量:</span>
            <span class="stats-value">{{ stats.edgeCount }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">泳道数量:</span>
            <span class="stats-value">{{ stats.laneCount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, computed, onUnmounted } from 'vue';
  import { Icon } from '/@/components/Icon';
  import type { NodePropertyData } from './types/workflow';

  interface Props {
    selectedNode?: any;
    selectedEdge?: any;
    canvasData?: any;
    width?: number;
    graph?: any; // 添加graph属性以便获取泳道信息
  }

  interface Emits {
    (e: 'nodePropertiesChange', properties: NodePropertyData): void;
    (e: 'edgePropertiesChange', properties: any): void;
    (e: 'canvasPropertiesChange', properties: any): void;
    (e: 'widthChange', width: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    width: 400,
  });

  const emit = defineEmits<Emits>();

  const collapsed = ref(false);

  // 属性变更标记
  const hasUnsavedChanges = ref(false);
  const saveTimeout = ref<NodeJS.Timeout | null>(null);

  // 节点属性
  const nodeProperties = reactive<NodePropertyData>({
    name: '',
    assignee: '',
    assigneeOrgCode: '',
    handler: '',
    type: '',
    description: '',
  });

  // 边属性
  const edgeProperties = reactive({
    label: '',
    condition: '',
    description: '',
  });

  // 画布属性
  const canvasProperties = reactive({
    name: '',
    businessKey: '',
    version: '',
    description: '',
  });

  // 统计信息
  const stats = computed(() => {
    if (!props.canvasData) {
      return { nodeCount: 0, edgeCount: 0, laneCount: 0 };
    }

    // 优先使用已计算的统计信息
    if (typeof props.canvasData.nodeCount === 'number') {
      return {
        nodeCount: props.canvasData.nodeCount,
        edgeCount: props.canvasData.edgeCount,
        laneCount: props.canvasData.laneCount,
      };
    }

    // 如果没有预计算的统计信息，从cells中计算
    const cells = props.canvasData.cells || [];
    const nodeCount = cells.filter((cell: any) => cell.shape && !cell.source && !cell.target).length;
    const edgeCount = cells.filter((cell: any) => cell.source && cell.target).length;
    const laneCount = cells.filter((cell: any) => cell.shape === 'lane').length;

    return { nodeCount, edgeCount, laneCount };
  });

  /**
   * 获取节点类型标签
   */
  const getNodeTypeLabel = (shape: string) => {
    const typeMap: Record<string, string> = {
      'custom-rect': '基础节点',
      lane: '泳道',
      'lane-rect': '泳道节点',
      'lane-polygon': '多边形节点',
    };
    return typeMap[shape] || '未知类型';
  };

  /**
   * 获取节点所属的泳道
   */
  const getNodeLane = (node: any) => {
    if (!node || !props.graph) return null;

    // 如果节点本身就是泳道，返回自己
    if (node.shape === 'lane') {
      return node;
    }

    // 查找父节点（泳道）
    const parentId = node.prop?.('parent') || node.parent;
    if (parentId) {
      const parentNode = props.graph.getCellById(parentId);
      if (parentNode && parentNode.shape === 'lane') {
        return parentNode;
      }
    }

    return null;
  };



  /**
   * 获取委托人和委托机构的值（从泳道获取）
   */
  const getLaneAssigneeInfo = () => {
    const lane = getNodeLane(props.selectedNode);
    if (lane && lane.data) {
      return {
        assignee: lane.data.assignee || '',
        assigneeOrgCode: lane.data.assigneeOrgCode || '',
      };
    }
    return {
      assignee: '',
      assigneeOrgCode: '',
    };
  };

  /**
   * 切换折叠状态
   */
  const toggleCollapse = () => {
    collapsed.value = !collapsed.value;
    const newWidth = collapsed.value ? 40 : props.width;
    emit('widthChange', newWidth);
  };

  /**
   * 处理节点属性输入（防抖保存）
   */
  const handleNodePropertyInput = () => {
    hasUnsavedChanges.value = true;

    // 清除之前的定时器
    if (saveTimeout.value) {
      clearTimeout(saveTimeout.value);
    }

    // 设置新的定时器，500ms后自动保存
    saveTimeout.value = setTimeout(() => {
      updateNodeProperties();
    }, 500);
  };

  /**
   * 更新节点属性
   */
  const updateNodeProperties = () => {
    // 清除定时器
    if (saveTimeout.value) {
      clearTimeout(saveTimeout.value);
      saveTimeout.value = null;
    }

    hasUnsavedChanges.value = false;
    emit('nodePropertiesChange', { ...nodeProperties });
  };

  /**
   * 处理边属性输入（防抖保存）
   */
  const handleEdgePropertyInput = () => {
    hasUnsavedChanges.value = true;

    // 清除之前的定时器
    if (saveTimeout.value) {
      clearTimeout(saveTimeout.value);
    }

    // 设置新的定时器，500ms后自动保存
    saveTimeout.value = setTimeout(() => {
      updateEdgeProperties();
    }, 500);
  };

  /**
   * 更新边属性
   */
  const updateEdgeProperties = () => {
    // 清除定时器
    if (saveTimeout.value) {
      clearTimeout(saveTimeout.value);
      saveTimeout.value = null;
    }

    hasUnsavedChanges.value = false;
    emit('edgePropertiesChange', { ...edgeProperties });
  };

  /**
   * 更新画布属性
   */
  const updateCanvasProperties = () => {
    emit('canvasPropertiesChange', { ...canvasProperties });
  };

  // 监听选中节点变化
  watch(
    () => props.selectedNode,
    (newNode, oldNode) => {
      // 如果有未保存的更改，先保存当前节点的属性
      if (hasUnsavedChanges.value && oldNode) {
        updateNodeProperties();
      }

      if (newNode) {
        const data = newNode.data || {};

        // 根据节点类型设置属性
        if (newNode.shape === 'lane') {
          // 泳道节点：直接从自身data获取委托人/委托机构
          Object.assign(nodeProperties, {
            name: data.name || newNode.label || '',
            assignee: data.assignee || '',
            assigneeOrgCode: data.assigneeOrgCode || '',
            handler: data.handler || '',
            type: data.type || '',
            description: data.description || '',
          });
        } else {
          // 普通节点：委托人/委托机构从所属泳道获取，其他属性从自身获取
          const laneInfo = getLaneAssigneeInfo();
          Object.assign(nodeProperties, {
            name: data.name || newNode.label || '',
            assignee: laneInfo.assignee, // 从泳道获取
            assigneeOrgCode: laneInfo.assigneeOrgCode, // 从泳道获取
            handler: data.handler || '',
            type: data.type || '',
            description: data.description || '',
          });
        }

        // 重置未保存标记
        hasUnsavedChanges.value = false;
      }
    },
    { immediate: true }
  );

  // 监听选中边变化
  watch(
    () => props.selectedEdge,
    (newEdge, oldEdge) => {
      // 如果有未保存的更改，先保存当前边的属性
      if (hasUnsavedChanges.value && oldEdge) {
        updateEdgeProperties();
      }

      if (newEdge) {
        const data = newEdge.data || {};
        const labels = newEdge.labels || [];
        Object.assign(edgeProperties, {
          label: labels.length > 0 ? labels[0].attrs?.text?.text || '' : '',
          condition: data.condition || '',
          description: data.description || '',
        });

        // 重置未保存标记
        hasUnsavedChanges.value = false;
      }
    },
    { immediate: true }
  );

  // 监听画布数据变化
  watch(
    () => props.canvasData,
    (data) => {
      if (data) {
        Object.assign(canvasProperties, {
          name: data.name || '',
          businessKey: data.businessKey || '',
          version: data.version || '',
          description: data.description || '',
        });
      }
    },
    { immediate: true }
  );

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (saveTimeout.value) {
      clearTimeout(saveTimeout.value);
    }
  });
</script>

<style lang="less" scoped>
  .workflow-sidebar {
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #dfe3e8;
    position: relative;
    transition: width 0.3s ease;
    flex-shrink: 0; // 防止在flex容器中被压缩

    .sidebar-toggle {
      position: absolute;
      top: 50%;
      left: -12px;
      width: 24px;
      height: 24px;
      background-color: #fff;
      border: 1px solid #dfe3e8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transform: translateY(-50%);
      transition: all 0.3s;

      &:hover {
        background-color: #f5f5f5;
        border-color: #1890ff;
        color: #1890ff;
      }
    }

    .sidebar-content {
      height: 100%;
      overflow-y: auto;
      padding: 0;
    }

    .property-panel,
    .stats-panel {
      border-bottom: 1px solid #f0f0f0;

      .panel-header {
        padding: 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .node-type,
        .edge-type {
          font-size: 12px;
          color: #666;
          background-color: #e6f7ff;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }

      .panel-body {
        padding: 16px;

        .ant-form-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .ant-form-item-label {
          padding-bottom: 4px;

          label {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .stats-panel {
      .stats-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .stats-label {
          font-size: 12px;
          color: #666;
        }

        .stats-value {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  // 滚动条样式
  .sidebar-content::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-content::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .sidebar-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .sidebar-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .field-hint {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }
</style>
